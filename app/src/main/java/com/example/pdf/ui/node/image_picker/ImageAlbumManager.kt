package com.example.pdf.ui.node.image_picker

import android.content.Context
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.koin.core.annotation.Single

@Single
class ImageAlbumManager(private val context: Context) {

  // 使用StateFlow缓存策略，参考DocumentRepository
  private val _imagesByFolderFlow: MutableStateFlow<Map<String, List<ImageItem>>> = MutableStateFlow(emptyMap())
  val imagesByFolderFlow: StateFlow<Map<String, List<ImageItem>>> get() = _imagesByFolderFlow

  private val _albumsFlow: MutableStateFlow<List<ImageAlbum>> = MutableStateFlow(emptyList())
  val albumsFlow: StateFlow<List<ImageAlbum>> get() = _albumsFlow

  companion object {
    const val ALL_IMAGES_FOLDER = "All Images"
  }

  /**
   * 获取所有相册，如果缓存为空则触发加载
   */
  suspend fun getAllAlbums(): List<ImageAlbum> {
    val currentAlbums = _albumsFlow.value
    if (currentAlbums.isEmpty()) {
      onFetchImages()
    }
    return currentAlbums
  }

  /**
   * 获取指定相册的图片，如果缓存为空则触发加载
   */
  suspend fun getImagesForAlbum(album: ImageAlbum): List<ImageItem> {
    val currentImages = _imagesByFolderFlow.value
    if (currentImages.isEmpty()) {
      onFetchImages()
    }

    return when (album.id) {
      "all_images" -> currentImages[ALL_IMAGES_FOLDER] ?: emptyList()
      else -> currentImages[album.name] ?: emptyList()
    }
  }

  /**
   * 主要的数据获取方法，参考DocumentRepository.onFetchDocuments()
   */
  fun onFetchImages() {
    GlobalScope.launch {
      try {
        val imagesByFolder = loadAllImagesGroupedByFolder()
        val albums = createAlbumsFromImageMap(imagesByFolder)

        _imagesByFolderFlow.emit(imagesByFolder)
        _albumsFlow.emit(albums)
      } catch (e: Exception) {
        e.printStackTrace()
      }
    }
  }

  /**
   * 加载所有图片并按文件夹分组，返回Map<String, List<ImageItem>>
   */
  private suspend fun loadAllImagesGroupedByFolder(): Map<String, List<ImageItem>> = withContext(Dispatchers.IO) {
    val allImages = loadAllImagesFromStorage()
    val imagesByFolder = mutableMapOf<String, MutableList<ImageItem>>()

    // 添加"All Images"文件夹
    imagesByFolder[ALL_IMAGES_FOLDER] = allImages.toMutableList()

    // 按文件夹分组
    allImages.forEach { image ->
      val folderName = getBucketName(image.uri) ?: "Unknown"
      if (folderName != "Unknown") {
        imagesByFolder.getOrPut(folderName) { mutableListOf() }.add(image)
      }
    }

    // 转换为不可变Map
    imagesByFolder.mapValues { it.value.toList() }
  }

  /**
   * 从图片Map创建相册列表
   */
  private fun createAlbumsFromImageMap(imagesByFolder: Map<String, List<ImageItem>>): List<ImageAlbum> {
    val albums = mutableListOf<ImageAlbum>()

    imagesByFolder.forEach { (folderName, images) ->
      if (images.isNotEmpty()) {
        val albumId = if (folderName == ALL_IMAGES_FOLDER) {
          "all_images"
        } else {
          folderName.lowercase().replace(" ", "_")
        }

        albums.add(
          ImageAlbum(
            id = albumId,
            name = folderName,
            coverUri = images.firstOrNull()?.uri,
            imageCount = images.size
          )
        )
      }
    }

    // 确保"All Images"在第一位
    return albums.sortedBy { if (it.id == "all_images") 0 else 1 }
  }

  /**
   * 预加载缓存，可以在应用启动时调用
   */
  fun preloadCache() {
    onFetchImages()
  }

  /**
   * 清除缓存
   */
  fun clearCache() {
    GlobalScope.launch {
      _imagesByFolderFlow.emit(emptyMap())
      _albumsFlow.emit(emptyList())
    }
  }

  /**
   * 获取缓存状态信息（用于调试）
   */
  fun getCacheInfo(): String {
    val albumsCount = _albumsFlow.value.size
    val foldersCount = _imagesByFolderFlow.value.size
    val totalImages = _imagesByFolderFlow.value.values.sumOf { it.size }
    return "Albums: $albumsCount, Folders: $foldersCount, Total images: $totalImages"
  }

  private suspend fun loadAllImagesFromStorage(): List<ImageItem> = withContext(Dispatchers.IO) {
    val images = mutableListOf<ImageItem>()
    val contentResolver = context.contentResolver

    val collection = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
      MediaStore.Images.Media.getContentUri(MediaStore.VOLUME_EXTERNAL)
    } else {
      MediaStore.Images.Media.EXTERNAL_CONTENT_URI
    }

    val projection = arrayOf(
      MediaStore.Images.Media._ID,
      MediaStore.Images.Media.DISPLAY_NAME,
      MediaStore.Images.Media.DATE_ADDED,
      MediaStore.Images.Media.SIZE,
      MediaStore.Images.Media.BUCKET_DISPLAY_NAME
    )

    val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC"

    try {
      contentResolver.query(
        collection,
        projection,
        null,
        null,
        sortOrder
      )?.use { cursor ->
        val idColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID)
        val nameColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DISPLAY_NAME)
        val dateColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATE_ADDED)
        val sizeColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE)

        while (cursor.moveToNext()) {
          val id = cursor.getLong(idColumn)
          val name = cursor.getString(nameColumn) ?: ""
          val dateAdded = cursor.getLong(dateColumn)
          val size = cursor.getLong(sizeColumn)

          val uri = Uri.withAppendedPath(collection, id.toString())

          images.add(
            ImageItem(
              id = id.toString(),
              uri = uri,
              displayName = name,
              dateAdded = dateAdded,
              size = size
            )
          )
        }
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }

    images
  }



  private fun getBucketName(uri: Uri): String? {
    return try {
      val projection = arrayOf(MediaStore.Images.Media.BUCKET_DISPLAY_NAME)
      context.contentResolver.query(uri, projection, null, null, null)?.use { cursor ->
        if (cursor.moveToFirst()) {
          val bucketColumn = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.BUCKET_DISPLAY_NAME)
          cursor.getString(bucketColumn)
        } else null
      }
    } catch (e: Exception) {
      null
    }
  }
}
