package com.example.pdf.ui.node.image_picker

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

/**
 * 图片缓存预加载器
 *
 * 在应用启动时预加载图片数据，提升用户体验
 */
@Single
class ImageCachePreloader(
  private val imageAlbumManager: ImageAlbumManager
) {

  private val preloadScope = CoroutineScope(Dispatchers.IO + SupervisorJob())

  /**
   * 启动预加载
   * 建议在应用启动时调用，比如在 AppInitializer 中
   */
  fun startPreloading() {
    try {
      // 使用新的StateFlow策略预加载图片缓存
      imageAlbumManager.preloadCache()

      // 可以在这里添加更多预加载逻辑
      // 比如预加载最近使用的相册等

    } catch (e: Exception) {
      // 预加载失败不影响应用正常运行
      e.printStackTrace()
    }
  }

  /**
   * 清除所有缓存
   */
  fun clearAllCache() {
    imageAlbumManager.clearCache()
  }

  /**
   * 获取缓存状态
   */
  fun getCacheStatus(): String {
    return imageAlbumManager.getCacheInfo()
  }
}
